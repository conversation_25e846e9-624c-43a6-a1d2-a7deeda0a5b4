// DOM 元素
const uploadArea = document.getElementById('uploadArea');
const fileInput = document.getElementById('fileInput');
const progressContainer = document.getElementById('progressContainer');
const progressFill = document.getElementById('progressFill');
const progressText = document.getElementById('progressText');
const resultContainer = document.getElementById('resultContainer');
const errorContainer = document.getElementById('errorContainer');
const errorMessage = document.getElementById('errorMessage');
const previewImage = document.getElementById('previewImage');
const imageUrl = document.getElementById('imageUrl');
const copyBtn = document.getElementById('copyBtn');
const toast = document.getElementById('toast');

// 拖拽事件处理
uploadArea.addEventListener('dragover', (e) => {
    e.preventDefault();
    uploadArea.classList.add('dragover');
});

uploadArea.addEventListener('dragleave', (e) => {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
});

uploadArea.addEventListener('drop', (e) => {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        handleFile(files[0]);
    }
});

// 点击上传区域选择文件
uploadArea.addEventListener('click', () => {
    fileInput.click();
});

// 文件选择事件
fileInput.addEventListener('change', (e) => {
    if (e.target.files.length > 0) {
        handleFile(e.target.files[0]);
    }
});

// 复制按钮事件
copyBtn.addEventListener('click', () => {
    const url = imageUrl.value;
    navigator.clipboard.writeText(url).then(() => {
        showToast('链接已复制到剪贴板！');
    }).catch(() => {
        // 降级方案
        imageUrl.select();
        document.execCommand('copy');
        showToast('链接已复制到剪贴板！');
    });
});

// 处理文件上传
function handleFile(file) {
    // 验证文件类型
    const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
        showError('不支持的文件类型，请选择 PNG、JPG、JPEG、GIF 或 WEBP 格式的图片');
        return;
    }
    
    // 验证文件大小 (16MB)
    if (file.size > 16 * 1024 * 1024) {
        showError('文件大小不能超过 16MB');
        return;
    }
    
    // 显示上传进度
    showProgress();
    
    // 创建 FormData
    const formData = new FormData();
    formData.append('file', file);
    
    // 上传文件
    uploadFile(formData);
}

// 上传文件到服务器
function uploadFile(formData) {
    const xhr = new XMLHttpRequest();
    
    // 上传进度
    xhr.upload.addEventListener('progress', (e) => {
        if (e.lengthComputable) {
            const percentComplete = (e.loaded / e.total) * 100;
            updateProgress(percentComplete);
        }
    });
    
    // 上传完成
    xhr.addEventListener('load', () => {
        if (xhr.status === 200) {
            try {
                const response = JSON.parse(xhr.responseText);
                if (response.success) {
                    showResult(response.url);
                } else {
                    showError(response.error || '上传失败');
                }
            } catch (e) {
                showError('服务器响应格式错误');
            }
        } else {
            try {
                const response = JSON.parse(xhr.responseText);
                showError(response.error || '上传失败');
            } catch (e) {
                showError(`上传失败 (HTTP ${xhr.status})`);
            }
        }
    });
    
    // 上传错误
    xhr.addEventListener('error', () => {
        showError('网络错误，请检查网络连接');
    });
    
    // 发送请求
    xhr.open('POST', '/upload');
    xhr.send(formData);
}

// 显示上传进度
function showProgress() {
    hideAllContainers();
    progressContainer.style.display = 'block';
    updateProgress(0);
}

// 更新进度条
function updateProgress(percent) {
    progressFill.style.width = percent + '%';
    progressText.textContent = `上传中... ${Math.round(percent)}%`;
}

// 显示上传结果
function showResult(url) {
    hideAllContainers();
    resultContainer.style.display = 'block';
    
    // 设置预览图片
    previewImage.src = url;
    previewImage.onload = () => {
        // 图片加载完成后显示
        previewImage.style.opacity = '1';
    };
    
    // 设置图片链接
    imageUrl.value = url;
}

// 显示错误信息
function showError(message) {
    hideAllContainers();
    errorContainer.style.display = 'block';
    errorMessage.textContent = message;
}

// 隐藏所有容器
function hideAllContainers() {
    progressContainer.style.display = 'none';
    resultContainer.style.display = 'none';
    errorContainer.style.display = 'none';
}

// 重置上传状态
function resetUpload() {
    hideAllContainers();
    uploadArea.style.display = 'block';
    fileInput.value = '';
    previewImage.style.opacity = '0';
}

// 显示 Toast 提示
function showToast(message) {
    toast.textContent = message;
    toast.classList.add('show');
    
    setTimeout(() => {
        toast.classList.remove('show');
    }, 3000);
}

// 初始化
document.addEventListener('DOMContentLoaded', () => {
    resetUpload();
});

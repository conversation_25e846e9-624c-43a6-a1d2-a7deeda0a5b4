* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px;
}

.container {
    max-width: 600px;
    margin: 0 auto;
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    overflow: hidden;
}

h1 {
    text-align: center;
    padding: 30px 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    margin: 0;
    font-size: 24px;
    font-weight: 600;
}

.upload-area {
    margin: 30px;
    border: 3px dashed #ddd;
    border-radius: 12px;
    padding: 60px 20px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-area:hover {
    border-color: #667eea;
    background-color: #f8f9ff;
}

.upload-area.dragover {
    border-color: #667eea;
    background-color: #f0f4ff;
    transform: scale(1.02);
}

.upload-icon {
    font-size: 48px;
    margin-bottom: 20px;
}

.upload-text {
    font-size: 18px;
    color: #333;
    margin-bottom: 10px;
    font-weight: 500;
}

.upload-hint {
    color: #666;
    font-size: 14px;
    margin-bottom: 20px;
}

.upload-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 25px;
    font-size: 16px;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.upload-btn:hover {
    transform: translateY(-2px);
}

.progress-container {
    margin: 30px;
    text-align: center;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background-color: #f0f0f0;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 15px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    width: 0%;
    transition: width 0.3s ease;
}

.progress-text {
    color: #666;
    font-size: 14px;
}

.result-container {
    margin: 30px;
    text-align: center;
}

.result-container h3 {
    color: #28a745;
    margin-bottom: 20px;
    font-size: 20px;
}

.image-preview {
    margin-bottom: 20px;
}

.image-preview img {
    max-width: 100%;
    max-height: 200px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.url-container {
    margin-bottom: 20px;
}

.url-container label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
    text-align: left;
}

.url-input-group {
    display: flex;
    gap: 10px;
}

.url-input-group input {
    flex: 1;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background-color: #f8f9fa;
}

.copy-btn {
    background: #28a745;
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s ease;
}

.copy-btn:hover {
    background: #218838;
}

.upload-another-btn, .retry-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 25px;
    font-size: 16px;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.upload-another-btn:hover, .retry-btn:hover {
    transform: translateY(-2px);
}

.error-container {
    margin: 30px;
    text-align: center;
}

.error-message {
    background: #f8d7da;
    color: #721c24;
    padding: 15px;
    border-radius: 6px;
    margin-bottom: 20px;
    border: 1px solid #f5c6cb;
}

.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #28a745;
    color: white;
    padding: 15px 20px;
    border-radius: 6px;
    transform: translateX(400px);
    transition: transform 0.3s ease;
    z-index: 1000;
}

.toast.show {
    transform: translateX(0);
}

@media (max-width: 768px) {
    .container {
        margin: 10px;
    }
    
    .url-input-group {
        flex-direction: column;
    }
    
    .url-input-group input {
        margin-bottom: 10px;
    }
}

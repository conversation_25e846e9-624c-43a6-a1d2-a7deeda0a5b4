#!/usr/bin/env python3
"""
测试上传功能的脚本
"""
import requests
import io
from PIL import Image

def create_test_image():
    """创建一个测试图片"""
    # 创建一个简单的测试图片
    img = Image.new('RGB', (100, 100), color='red')
    img_bytes = io.BytesIO()
    img.save(img_bytes, format='JPEG')
    img_bytes.seek(0)
    return img_bytes

def test_upload():
    """测试上传功能"""
    print("创建测试图片...")
    test_image = create_test_image()
    
    print("上传图片到服务器...")
    files = {'file': ('test.jpg', test_image, 'image/jpeg')}
    
    try:
        response = requests.post('http://localhost:9090/upload', files=files)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ 上传成功!")
                print(f"图片链接: {result['url']}")
                print(f"文件名: {result['filename']}")
            else:
                print(f"❌ 上传失败: {result.get('error', '未知错误')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败，请确保服务器正在运行")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == '__main__':
    print("=" * 50)
    print("图片上传功能测试")
    print("=" * 50)
    test_upload()

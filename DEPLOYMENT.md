# 部署指南

## 生产环境部署

### 1. 使用 Gunicorn 部署

```bash
# 安装 gunicorn (已在 requirements.txt 中)
pip install gunicorn

# 启动应用
gunicorn -w 4 -b 0.0.0.0:8080 app:app
```

### 2. 使用 Docker 部署

创建 `Dockerfile`:

```dockerfile
FROM python:3.9-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

EXPOSE 8080

CMD ["gunicorn", "-w", "4", "-b", "0.0.0.0:8080", "app:app"]
```

构建和运行：

```bash
docker build -t image-upload-system .
docker run -p 8080:8080 image-upload-system
```

### 3. 环境变量配置

生产环境建议使用环境变量：

```bash
export SECRET_KEY="your-production-secret-key"
export FLASK_ENV="production"
export OSS_ACCESS_KEY_ID="your-access-key-id"
export OSS_ACCESS_KEY_SECRET="your-access-key-secret"
```

### 4. Nginx 反向代理配置

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 增加上传文件大小限制
        client_max_body_size 20M;
    }
}
```

### 5. 系统服务配置 (systemd)

创建 `/etc/systemd/system/image-upload.service`:

```ini
[Unit]
Description=Image Upload System
After=network.target

[Service]
User=www-data
Group=www-data
WorkingDirectory=/path/to/your/app
Environment=PATH=/path/to/your/venv/bin
ExecStart=/path/to/your/venv/bin/gunicorn -w 4 -b 127.0.0.1:8080 app:app
Restart=always

[Install]
WantedBy=multi-user.target
```

启动服务：

```bash
sudo systemctl daemon-reload
sudo systemctl enable image-upload
sudo systemctl start image-upload
```

## 安全建议

1. **更改默认密钥**: 修改 `SECRET_KEY`
2. **HTTPS**: 在生产环境中使用 HTTPS
3. **防火墙**: 配置适当的防火墙规则
4. **文件类型验证**: 已实现基本的文件类型检查
5. **文件大小限制**: 已设置 16MB 限制
6. **OSS 权限**: 确保 OSS 账户权限最小化

## 监控和日志

1. **应用日志**: 使用 Python logging 模块
2. **访问日志**: Nginx 或 Gunicorn 访问日志
3. **错误监控**: 可集成 Sentry 等错误监控服务
4. **性能监控**: 可使用 New Relic 或 DataDog

## 备份策略

1. **代码备份**: 使用 Git 版本控制
2. **OSS 备份**: 阿里云 OSS 自带高可用性
3. **配置备份**: 定期备份配置文件

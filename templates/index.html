<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片上传系统</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <div class="container">
        <h1>图片上传系统</h1>
        
        <!-- 上传区域 -->
        <div class="upload-area" id="uploadArea">
            <div class="upload-content">
                <div class="upload-icon">📁</div>
                <p class="upload-text">拖拽图片到这里或点击选择文件</p>
                <p class="upload-hint">支持 PNG, JPG, JPEG, GIF, WEBP 格式</p>
                <input type="file" id="fileInput" accept="image/*" style="display: none;">
                <button class="upload-btn" onclick="document.getElementById('fileInput').click()">选择文件</button>
            </div>
        </div>
        
        <!-- 上传进度 -->
        <div class="progress-container" id="progressContainer" style="display: none;">
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <p class="progress-text" id="progressText">上传中...</p>
        </div>
        
        <!-- 结果显示 -->
        <div class="result-container" id="resultContainer" style="display: none;">
            <h3>上传成功！</h3>
            <div class="image-preview">
                <img id="previewImage" src="" alt="预览图片">
            </div>
            <div class="url-container">
                <label>图片链接：</label>
                <div class="url-input-group">
                    <input type="text" id="imageUrl" readonly>
                    <button class="copy-btn" id="copyBtn">复制链接</button>
                </div>
            </div>
            <button class="upload-another-btn" onclick="resetUpload()">继续上传</button>
        </div>
        
        <!-- 错误提示 -->
        <div class="error-container" id="errorContainer" style="display: none;">
            <div class="error-message" id="errorMessage"></div>
            <button class="retry-btn" onclick="resetUpload()">重新上传</button>
        </div>
    </div>
    
    <!-- Toast 提示 -->
    <div class="toast" id="toast"></div>
    
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
</body>
</html>

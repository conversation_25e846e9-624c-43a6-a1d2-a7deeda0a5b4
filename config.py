import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    # OSS配置
    OSS_ACCESS_KEY_ID = "LTAI5tMWNMyAdqnpghJf51jy"
    OSS_ACCESS_KEY_SECRET = "******************************"
    OSS_BUCKET_NAME = "bonuspoints"
    OSS_ENDPOINT = "oss-cn-hangzhou.aliyuncs.com"
    OSS_BUCKET_DOMAIN = "bonuspoints.oss-cn-hangzhou.aliyuncs.com"
    OSS_UPLOAD_PATH = "tiku_images/"
    
    # Flask配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-secret-key-here'
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB max file size
    
    # 允许的文件扩展名
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'webp'}
    
    # 图片压缩配置
    IMAGE_QUALITY = 85  # JPEG质量
    MAX_IMAGE_SIZE = (1920, 1080)  # 最大尺寸

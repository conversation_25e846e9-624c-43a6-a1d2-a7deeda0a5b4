# 图片上传系统

基于 Flask 和阿里云 OSS 的图片上传系统，支持拖拽上传、图片压缩和链接复制功能。

## 功能特性

- ✅ 支持拖拽上传图片
- ✅ 自动图片压缩和重命名
- ✅ 上传到阿里云 OSS
- ✅ 返回图片链接并支持一键复制
- ✅ 支持多种图片格式 (PNG, JPG, JPEG, GIF, WEBP)
- ✅ 响应式设计，支持移动端

## 安装和运行

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置环境变量

编辑 `.env` 文件，设置你的密钥：

```
SECRET_KEY=your-secret-key-here
FLASK_ENV=development
```

### 3. 运行应用

```bash
python run.py
```

或者直接运行：

```bash
python app.py
```

### 4. 访问应用

打开浏览器访问：http://localhost:9091

## OSS 配置

当前配置的 OSS 信息（在 `config.py` 中）：

- **Bucket**: bonuspoints
- **域名**: bonuspoints.oss-cn-hangzhou.aliyuncs.com
- **上传路径**: /tiku_images/
- **AccessKey ID**: LTAI5tMWNMyAdqnpghJf51jy

## 使用方法

1. 打开网页
2. 拖拽图片到上传区域，或点击选择文件
3. 等待上传完成
4. 复制返回的图片链接

## 技术栈

- **后端**: Flask + Python
- **前端**: HTML5 + CSS3 + JavaScript
- **存储**: 阿里云 OSS
- **图片处理**: Pillow

## 文件结构

```
.
├── app.py              # 主应用文件
├── config.py           # 配置文件
├── requirements.txt    # 依赖列表
├── run.py             # 启动脚本
├── .env               # 环境变量
├── templates/
│   └── index.html     # 主页模板
└── static/
    ├── css/
    │   └── style.css  # 样式文件
    └── js/
        └── main.js    # 前端脚本
```

## API 接口

### POST /upload

上传图片接口

**请求参数:**
- `file`: 图片文件 (multipart/form-data)

**响应格式:**
```json
{
    "success": true,
    "url": "https://bonuspoints.oss-cn-hangzhou.aliyuncs.com/tiku_images/gm_20250614202840.jpg",
    "filename": "gm_20250614202840.jpg",
    "message": "上传成功"
}
```

## 注意事项

- 支持的图片格式：PNG, JPG, JPEG, GIF, WEBP
- 最大文件大小：16MB
- 图片会自动压缩到最大尺寸 1920x1080
- 压缩质量：85%
- 文件名格式：gm_年月日时分秒.扩展名

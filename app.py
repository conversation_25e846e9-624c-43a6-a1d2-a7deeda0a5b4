from datetime import datetime
from flask import Flask, request, jsonify, render_template
from PIL import Image
import oss2
from config import Config
import io

app = Flask(__name__)
app.config.from_object(Config)

# 初始化OSS客户端
auth = oss2.Auth(Config.OSS_ACCESS_KEY_ID, Config.OSS_ACCESS_KEY_SECRET)
bucket = oss2.Bucket(auth, Config.OSS_ENDPOINT, Config.OSS_BUCKET_NAME)

def allowed_file(filename):
    """检查文件扩展名是否允许"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in Config.ALLOWED_EXTENSIONS

def compress_image(image_data, format='JPEG'):
    """压缩图片"""
    try:
        # 打开图片
        image = Image.open(io.BytesIO(image_data))
        
        # 转换为RGB模式（如果是RGBA或其他模式）
        if image.mode in ('RGBA', 'LA', 'P'):
            # 创建白色背景
            background = Image.new('RGB', image.size, (255, 255, 255))
            if image.mode == 'P':
                image = image.convert('RGBA')
            background.paste(image, mask=image.split()[-1] if image.mode == 'RGBA' else None)
            image = background
        elif image.mode != 'RGB':
            image = image.convert('RGB')
        
        # 调整图片尺寸
        image.thumbnail(Config.MAX_IMAGE_SIZE, Image.Resampling.LANCZOS)
        
        # 压缩图片
        output = io.BytesIO()
        image.save(output, format=format, quality=Config.IMAGE_QUALITY, optimize=True)
        output.seek(0)
        
        return output.getvalue()
    except Exception as e:
        print(f"图片压缩错误: {e}")
        return image_data

def generate_filename(original_filename):
    """生成新的文件名"""
    # 获取文件扩展名
    ext = original_filename.rsplit('.', 1)[1].lower() if '.' in original_filename else 'jpg'

    # 生成时间戳（精确到秒）
    timestamp = datetime.now().strftime('%Y%m%d%H%M%S')

    # 组合新文件名：gm_时间戳.扩展名
    new_filename = f"gm_{timestamp}.{ext}"
    return new_filename

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/upload', methods=['POST'])
def upload_file():
    """上传文件API"""
    try:
        # 检查是否有文件
        if 'file' not in request.files:
            return jsonify({'error': '没有选择文件'}), 400
        
        file = request.files['file']
        
        # 检查文件名
        if file.filename == '':
            return jsonify({'error': '没有选择文件'}), 400
        
        # 检查文件类型
        if not allowed_file(file.filename):
            return jsonify({'error': '不支持的文件类型'}), 400
        
        # 读取文件数据
        file_data = file.read()
        
        # 压缩图片
        compressed_data = compress_image(file_data)
        
        # 生成新文件名
        new_filename = generate_filename(file.filename)
        
        # 构建OSS路径
        oss_path = Config.OSS_UPLOAD_PATH + new_filename
        
        # 上传到OSS
        result = bucket.put_object(oss_path, compressed_data)
        
        if result.status == 200:
            # 构建图片URL
            image_url = f"https://{Config.OSS_BUCKET_DOMAIN}/{oss_path}"
            
            return jsonify({
                'success': True,
                'url': image_url,
                'filename': new_filename,
                'message': '上传成功'
            })
        else:
            return jsonify({'error': '上传失败'}), 500
            
    except Exception as e:
        print(f"上传错误: {e}")
        return jsonify({'error': f'上传失败: {str(e)}'}), 500

@app.route('/health')
def health_check():
    """健康检查"""
    return jsonify({'status': 'ok'})

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=8080)
